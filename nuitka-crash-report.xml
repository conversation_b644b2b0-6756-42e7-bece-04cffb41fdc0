<?xml version='1.0' encoding='utf8'?>
<nuitka-compilation-report nuitka_version="2.7.12" nuitka_commercial_version="not installed" mode="onefile" completion="error exit message (1)" exit_message="pyside6: Error, failed to locate the 'PySide6' installation.">
  <performance>
    <memory_usage name="after_launch" value="46882816" />
  </performance>
  <data_composer blob_size="None" />
  <command_line>
    <option value="--standalone" />
    <option value="--jobs=8" />
    <option value="--onefile" />
    <option value="--windows-disable-console" />
    <option value="--enable-plugin=pyside6" />
    <option value="--include-module=comtypes" />
    <option value="--include-module=pythoncom" />
    <option value="--include-module=pywin32" />
    <option value="--include-module=pywinauto" />
    <option value="--include-package=ui" />
    <option value="--include-package=core" />
    <option value="--include-package=config" />
    <option value="--include-data-dir=resource=resource" />
    <option value="--assume-yes-for-downloads" />
    <option value="--remove-output" />
    <option value="--no-progress-bar" />
    <option value="--quiet" />
    <option value="--output-filename=微信公众号自动化管理工具-快速版.exe" />
    <option value="--output-dir=dist_fast" />
    <option value="main.py" />
  </command_line>
  <plugins>
    <plugin name="anti-bloat" user_enabled="no" />
    <plugin name="data-files" user_enabled="no" />
    <plugin name="delvewheel" user_enabled="no" />
    <plugin name="dll-files" user_enabled="no" />
    <plugin name="eventlet" user_enabled="no" />
    <plugin name="gevent" user_enabled="no" />
    <plugin name="gi" user_enabled="no" />
    <plugin name="glfw" user_enabled="no" />
    <plugin name="implicit-imports" user_enabled="no" />
    <plugin name="kivy" user_enabled="no" />
    <plugin name="matplotlib" user_enabled="no" />
    <plugin name="multiprocessing" user_enabled="no" />
    <plugin name="options-nanny" user_enabled="no" />
    <plugin name="pbr-compat" user_enabled="no" />
    <plugin name="pkg-resources" user_enabled="no" />
    <plugin name="playwright" user_enabled="no" />
    <plugin name="pyside6" user_enabled="yes" />
    <plugin name="pywebview" user_enabled="no" />
    <plugin name="spacy" user_enabled="no" />
    <plugin name="transformers" user_enabled="no" />
  </plugins>
  <onefile cache_mode="temporary" unpack_dir="{TEMP}\onefile_{PID}_{TIME}" />
  <distributions />
  <python python_exe="${sys.prefix}\python.exe" python_flavor="Anaconda Python" python_version="3.11.9" os_name="Windows" os_release="11" arch_name="x86_64" filesystem_encoding="utf-8">
    <search_path>
      <path value="${cwd}" />
      <path value="${sys.prefix}\DLLs" />
      <path value="${sys.prefix}\Lib" />
      <path value="${sys.prefix}" />
      <path value="${sys.prefix}\Lib\site-packages" />
      <path value="${sys.prefix}\Lib\site-packages\win32" />
      <path value="${sys.prefix}\Lib\site-packages\win32\lib" />
      <path value="${sys.prefix}\Lib\site-packages\Pythonwin" />
      <path value="${sys.prefix}\Lib\site-packages\setuptools\_vendor" />
    </search_path>
  </python>
  <output run_filename="failed too early" />
</nuitka-compilation-report>
