#!/usr/bin/env python3
"""
使用Nuitka打包微信公众号自动化管理工具 - 优化版本

优化特性:
- 多核并行编译 (--jobs=0)
- 启用缓存机制 (--cache-dir)
- 链接时优化 (--lto=yes)
- 自动依赖检测 (减少手动指定模块)
- 智能文件复制 (避免重复复制)
- 清理功能 (--clean参数)

使用方法:
  python build_nuitka.py          # 正常编译
  python build_nuitka.py --clean  # 清理后编译
"""

import os
import sys
import subprocess
import shutil
import time
from pathlib import Path


def build_with_nuitka():
    """使用Nuitka构建应用程序"""

    print("开始使用Nuitka打包...")

    # 检查是否存在缓存目录，如果不存在则创建
    cache_dir = Path(".nuitka_cache")
    cache_dir.mkdir(exist_ok=True)

    # 基本的nuitka命令 - 优化版本
    cmd = [
        sys.executable, "-m", "nuitka",
        "--standalone",
        "--onefile",
        "--windows-disable-console",

        # 启用插件
        "--enable-plugin=pyside6",
        "--enable-plugin=multiprocessing",

        # 图标设置
        "--windows-icon-from-ico=icon.ico" if os.path.exists("icon.ico") else "",

        # 性能优化选项
        "--jobs=0",  # 使用所有可用CPU核心
        "--lto=yes",  # 启用链接时优化
        "--clang",  # 使用clang编译器（如果可用）

        # 缓存设置
        f"--cache-dir={cache_dir.absolute()}",
        "--assume-yes-for-downloads",

        # 只包含核心必需的模块，让Nuitka自动检测其他依赖
        "--include-module=comtypes",
        "--include-module=pythoncom",
        "--include-module=pywintypes",
        "--include-module=win32com.client",
        "--include-module=pywinauto",

        # 包含项目模块（让Nuitka自动检测子模块）
        "--include-package=ui",
        "--include-package=core",
        "--include-package=config",
        "--include-package=weixin",

        # 只包含必要的数据文件
        "--include-data-dir=resource=resource",
        "--include-data-file=requirements.txt=requirements.txt",

        # 输出设置
        "--output-filename=微信公众号自动化管理工具.exe",
        "--output-dir=dist_nuitka",

        # 显示设置
        "--show-progress",
        "--show-memory",
        "--quiet",  # 减少输出信息

        # 优化设置
        "--python-flag=no_site",  # 不加载site模块
        "--python-flag=no_warnings",  # 禁用警告

        # 主程序文件
        "main.py"
    ]

    # 过滤空字符串
    cmd = [arg for arg in cmd if arg]

    print("执行命令:")
    print(" ".join(cmd))
    print()

    try:
        # 执行nuitka命令
        result = subprocess.run(cmd, check=True, capture_output=False)
        print("\n✅ Nuitka打包成功!")

        # 复制必要的文件到输出目录
        copy_additional_files()

        return True

    except subprocess.CalledProcessError as e:
        print(f"\n❌ Nuitka打包失败: {e}")
        return False
    except Exception as e:
        print(f"\n❌ 打包过程出错: {e}")
        return False


def copy_additional_files():
    """复制额外的文件到输出目录（优化版本）"""

    dist_dir = Path("dist_nuitka")
    if not dist_dir.exists():
        print("输出目录不存在，跳过文件复制")
        return

    print("复制必要的附加文件...")

    # 只复制必要的文件和目录（减少复制时间）
    items_to_copy = [
        ("README.md", "README.md"),
        ("LICENSE", "LICENSE"),
        ("data", "data"),  # 只复制data目录，其他目录已通过--include-data-dir包含
        ("downloads", "downloads"),
    ]

    for src, dst in items_to_copy:
        src_path = Path(src)
        dst_path = dist_dir / dst

        try:
            if src_path.exists():
                if src_path.is_file():
                    # 检查文件是否已存在且内容相同，避免重复复制
                    if not dst_path.exists() or src_path.stat().st_mtime > dst_path.stat().st_mtime:
                        shutil.copy2(src_path, dst_path)
                        print(f"已复制文件: {src}")
                elif src_path.is_dir():
                    # 只在目录不存在或源目录更新时才复制
                    if not dst_path.exists():
                        shutil.copytree(src_path, dst_path)
                        print(f"已复制目录: {src}")
                    else:
                        print(f"目录已存在，跳过: {src}")
        except Exception as e:
            print(f"复制 {src} 时出错: {e}")

    # 创建使用说明
    create_usage_guide(dist_dir)


def create_usage_guide(dist_dir):
    """创建使用说明文件"""

    usage_content = """微信公众号自动化管理工具 - 使用说明

🚀 快速开始:
1. 双击"微信公众号自动化管理工具.exe"启动程序
2. 首次使用请在"设置"中配置微信程序路径

📁 目录说明:
- resource/ : 配置文件目录
  - config.json : 程序配置
  - accounts.csv : 账号数据
  - official_infos.json : 公众号信息
- data/ : 数据存储目录
  - *.csv : 各账号采集数据

📋 主要功能:
- 账号管理: 添加、编辑、删除公众号账号
- 批量关注: 自动关注指定的公众号列表  
- 数据采集: 自动采集公众号信息和文章数据
- 数据查看: 查看、搜索、筛选、导出数据
- 全文下载: 下载文章全文为MD/HTML格式

💻 系统要求:
- Windows 10/11
- 微信PC版 (最新版本)

⚠️ 重要提醒:
- 使用时请遵守微信使用条款，避免频繁操作
- 首次启动可能需要几秒钟，请耐心等待

🔧 技术信息:
- 打包工具: Nuitka
- Python版本: 3.12
- 构建时间: """ + str(Path().resolve()) + """

如有问题，请查看README.md文档或联系技术支持。
"""

    usage_file = dist_dir / "使用说明.txt"
    try:
        with open(usage_file, 'w', encoding='utf-8') as f:
            f.write(usage_content)
        print(f"已创建使用说明: {usage_file}")
    except Exception as e:
        print(f"创建使用说明失败: {e}")


def clean_cache():
    """清理旧的缓存文件以节省空间"""

    cache_dirs = [".nuitka_cache", "dist_nuitka", "__pycache__"]

    for cache_dir in cache_dirs:
        cache_path = Path(cache_dir)
        if cache_path.exists() and cache_path.is_dir():
            try:
                # 只清理__pycache__，保留nuitka缓存以加速后续编译
                if cache_dir == "__pycache__":
                    shutil.rmtree(cache_path)
                    print(f"已清理缓存目录: {cache_dir}")
                elif cache_dir == "dist_nuitka":
                    # 只清理旧的exe文件，保留其他文件
                    exe_files = list(cache_path.glob("*.exe"))
                    for exe_file in exe_files:
                        exe_file.unlink()
                        print(f"已删除旧的可执行文件: {exe_file.name}")
            except Exception as e:
                print(f"清理 {cache_dir} 时出错: {e}")


def check_dependencies():
    """检查依赖是否安装（优化版本）"""

    print("快速检查核心依赖...")

    # 只检查最关键的依赖
    critical_deps = [
        ("nuitka", "Nuitka"),
        ("PySide6", "PySide6"),
    ]

    for module, name in critical_deps:
        try:
            imported_module = __import__(module)
            if hasattr(imported_module, '__version__'):
                version = imported_module.__version__
            else:
                # 特殊处理Nuitka版本获取
                if module == "nuitka":
                    try:
                        from nuitka.Version import getNuitkaVersion
                        version = getNuitkaVersion()
                    except ImportError:
                        version = "未知版本"
                else:
                    version = "已安装"
            print(f"✅ {name}: {version}")
        except ImportError:
            print(f"❌ 未安装{name}")
            return False

    print("✅ 核心依赖检查通过")
    return True


if __name__ == "__main__":
    print("=" * 60)
    print("微信公众号自动化管理工具 - Nuitka打包脚本 (优化版)")
    print("=" * 60)
    print()

    # 检查命令行参数
    clean_first = "--clean" in sys.argv or "-c" in sys.argv

    if clean_first:
        print("🧹 清理旧文件...")
        clean_cache()
        print()

    # 检查依赖
    if not check_dependencies():
        print("\n❌ 请先安装必要的依赖")
        print("运行: pip install nuitka PySide6")
        sys.exit(1)

    print()

    # 显示优化信息
    print("🚀 优化特性:")
    print("  • 多核并行编译")
    print("  • 启用缓存机制")
    print("  • 链接时优化(LTO)")
    print("  • 自动依赖检测")
    print("  • 减少不必要的模块包含")
    print()

    # 开始打包
    import time
    start_time = time.time()

    success = build_with_nuitka()

    end_time = time.time()
    build_time = end_time - start_time

    if success:
        print(f"\n🎉 打包完成! 耗时: {build_time:.1f}秒")
        print("输出目录: dist_nuitka/")
        print("可执行文件: dist_nuitka/微信公众号自动化管理工具.exe")
        print("\n💡 提示:")
        print("  • 下次编译会更快（利用缓存）")
        print("  • 使用 --clean 参数清理旧文件")
    else:
        print(f"\n💥 打包失败! 耗时: {build_time:.1f}秒")
        print("\n🔧 故障排除:")
        print("  • 检查是否安装了所有依赖")
        print("  • 尝试使用 --clean 参数重新编译")
        print("  • 查看上方的错误信息")
        sys.exit(1)
