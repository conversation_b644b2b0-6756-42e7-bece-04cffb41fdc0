# -*- coding: utf-8 -*-

import sys
import os
import subprocess

exit_code = subprocess.call(
    ['D:\\Development\\Python\\anaconda3\\envs\\python_312\\python.exe', '-W', 'ignore', 'D:\\Development\\Python\\anaconda3\\envs\\python_312\\Lib\\site-packages\\nuitka\\build\\inline_copy\\bin\\scons.py', '--quiet', '-f', 'D:\\Development\\Python\\anaconda3\\envs\\python_312\\Lib\\site-packages\\nuitka\\build\\Backend.scons', '--jobs', '16', '--warn=no-deprecated', '--no-site-dir', 'nuitka_src=D:\\Development\\Python\\anaconda3\\envs\\python_312\\Lib\\site-packages\\nuitka\\build', 'python_version=3.12', 'python_prefix=D:\\Development\\Python\\anaconda3\\envs\\python_312', 'experimental=', 'debug_modes=', 'deployment=false', 'no_deployment=', 'assume_yes_for_downloads=true', 'console_mode=disable', 'noelf_mode=true', 'anaconda_python=true', 'cpp_defines=_NUITKA_PLUGIN_MULTIPROCESSING_ENABLED=1', 'target_arch=x86_64', 'module_mode=false', 'dll_mode=true', 'exe_mode=false', 'standalone_mode=true', 'onefile_mode=true', 'onefile_temp_mode=true', 'gil_mode=true', 'source_dir=.', 'nuitka_python=false', 'debug_mode=false', 'debugger_mode=false', 'python_debug=false', 'full_compat=false', 'trace_mode=false', 'file_reference_mode=runtime', 'compiled_module_count=694', 'result_exe=D:\\Project\\PycharmProjects\\lawyee\\Wechat-Auto\\dist_nuitka\\main.dist\\main.dll', 'frozen_modules=155', 'python_sysflag_no_site=true'],
    env={'ALLUSERSPROFILE': 'C:\\ProgramData','APPCODE_VM_OPTIONS': 'D:\\Development\\JetBrains\\DataGrip jetbra\\jetbra\\vmoptions\\appcode.vmoptions','APPDATA': 'C:\\Users\\<USER>\\AppData\\Roaming','CHOCOLATEYINSTALL': 'C:\\Users\\<USER>\\AppData\\Local\\UniGetUI\\Chocolatey','CLION_VM_OPTIONS': 'D:\\Development\\JetBrains\\DataGrip jetbra\\jetbra\\vmoptions\\clion.vmoptions','COMMONPROGRAMFILES': 'C:\\Program Files\\Common Files','COMMONPROGRAMFILES(X86)': 'C:\\Program Files (x86)\\Common Files','COMMONPROGRAMW6432': 'C:\\Program Files\\Common Files','COMPUTERNAME': 'DESKTOP-MRIK9I3','COMSPEC': 'C:\\WINDOWS\\system32\\cmd.exe','CONDA_DEFAULT_ENV': 'python_312','CONDA_PREFIX': 'D:\\Development\\Python\\anaconda3\\envs\\python_312','CONDA_PROMPT_MODIFIER': '(python_312) ','CONDA_SHLVL': '1','DATAGRIP_VM_OPTIONS': 'D:\\Development\\JetBrains\\DataGrip jetbra\\jetbra\\vmoptions\\datagrip.vmoptions','DATASPELL_VM_OPTIONS': 'D:\\Development\\JetBrains\\DataGrip jetbra\\jetbra\\vmoptions\\dataspell.vmoptions','DEVECOSTUDIO_VM_OPTIONS': 'D:\\Development\\JetBrains\\DataGrip jetbra\\jetbra\\vmoptions\\devecostudio.vmoptions','DRIVERDATA': 'C:\\Windows\\System32\\Drivers\\DriverData','GATEWAY_VM_OPTIONS': 'D:\\Development\\JetBrains\\DataGrip jetbra\\jetbra\\vmoptions\\gateway.vmoptions','GOLAND_VM_OPTIONS': 'D:\\Development\\JetBrains\\DataGrip jetbra\\jetbra\\vmoptions\\goland.vmoptions','HF_HOME': 'D:\\Development\\Environment\\llm_models\\huggingface_cache','HOMEDRIVE': 'C:','HOMEPATH': '\\Users\\JieMoc','HYOFFICEAI_PATH': 'D:\\Program Files\\OfficeAI\\HaiYingSoft\\OfficeAI','IDEA_VM_OPTIONS': 'D:\\Development\\JetBrains\\DataGrip jetbra\\jetbra\\vmoptions\\idea.vmoptions','JAVA_HOME': 'D:\\Development\\Java\\Java-8u181\\jdk1.8.0_181','JETBRAINSCLIENT_VM_OPTIONS': 'D:\\Development\\JetBrains\\DataGrip jetbra\\jetbra\\vmoptions\\jetbrainsclient.vmoptions','JETBRAINS_CLIENT_VM_OPTIONS': 'D:\\Development\\JetBrains\\DataGrip jetbra\\jetbra\\vmoptions\\jetbrains_client.vmoptions','LOCALAPPDATA': 'C:\\Users\\<USER>\\AppData\\Local','LOGONSERVER': '\\\\DESKTOP-MRIK9I3','NUMBER_OF_PROCESSORS': '16','NVM_HOME': 'D:\\Development\\Web\\nvm','NVM_SYMLINK': 'D:\\Development\\Web\\nodejs','OLLAMA_MODELS': 'D:\\Development\\Environment\\llm_models\\olamaCache','ONEDRIVE': 'D:\\OneDrive','ONEDRIVECONSUMER': 'D:\\OneDrive','OS': 'Windows_NT','PATH': 'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\cv2\\../../x64/vc14/bin;D:\\Development\\Python\\anaconda3\\envs\\python_312\\Lib\\site-packages\\PySide6;D:\\Development\\Python\\anaconda3\\envs\\python_312;D:\\Development\\Python\\anaconda3\\envs\\python_312\\Library\\mingw-w64\\bin;D:\\Development\\Python\\anaconda3\\envs\\python_312\\Library\\usr\\bin;D:\\Development\\Python\\anaconda3\\envs\\python_312\\Library\\bin;D:\\Development\\Python\\anaconda3\\envs\\python_312\\Scripts;D:\\Development\\Python\\anaconda3\\envs\\python_312\\bin;D:\\Development\\Python\\anaconda3\\condabin;C:\\Program Files\\Common Files\\Oracle\\Java\\javapath;C:\\Users\\<USER>\\.nvmd\\bin;D:\\Development\\Environment\\apache-maven-3.8.3\\bin;D:\\Development\\Tools\\VMware\\VMware Workstation\\bin;C:\\Program Files (x86)\\Common Files\\Oracle\\Java\\javapath;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0;C:\\WINDOWS\\System32\\OpenSSH;C:\\Recovery\\OEM\\Backup;C:\\Program Files\\dotnet;D:\\Development\\Python\\anaconda3;D:\\Development\\Python\\anaconda3\\Scripts;D:\\Development\\Python\\anaconda3\\Library\\lib;D:\\Development\\Tools\\Git\\cmd;D:\\Development\\Tools\\TortoiseSVN\\bin;D:\\Development\\Web\\nvm;D:\\Development\\Web\\nodejs;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\JetBrains\\Toolbox\\scripts;D:\\Development\\JetBrains\\Toolbox\\scripts;.;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0;C:\\WINDOWS\\System32\\OpenSSH;C:\\Users\\<USER>\\AppData\\Local\\UniGetUI\\Chocolatey\\bin;D:\\Development\\Scoop\\shims;D:\\Development\\Java\\Java-8u181\\jdk1.8.0_181\\BIN;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Ollama;C:\\Users\\<USER>\\.cache\\lm-studio\\bin;D:\\Development\\Tools\\Microsoft VS Code\\bin;D:\\Development\\Python\\anaconda3\\Library\\Bin','PATHEXT': '.COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC','PHPSTORM_VM_OPTIONS': 'D:\\Development\\JetBrains\\DataGrip jetbra\\jetbra\\vmoptions\\phpstorm.vmoptions','PROCESSOR_ARCHITECTURE': 'AMD64','PROCESSOR_IDENTIFIER': 'AMD64 Family 25 Model 116 Stepping 1, AuthenticAMD','PROCESSOR_LEVEL': '25','PROCESSOR_REVISION': '7401','PROGRAMDATA': 'C:\\ProgramData','PROGRAMFILES': 'C:\\Program Files','PROGRAMFILES(X86)': 'C:\\Program Files (x86)','PROGRAMW6432': 'C:\\Program Files','PROMPT': '(python_312) $P$G','PSMODULEPATH': '%ProgramFiles%\\WindowsPowerShell\\Modules;C:\\WINDOWS\\system32\\WindowsPowerShell\\v1.0\\Modules','PUBLIC': 'C:\\Users\\<USER>\\Development\\JetBrains\\DataGrip jetbra\\jetbra\\vmoptions\\pycharm.vmoptions','PYSIDE6_OPTION_PYTHON_ENUM': 'True','PYTHONHASHSEED': '0','PYTHONIOENCODING': 'UTF-8','PYTHONUNBUFFERED': '1','RIDER_VM_OPTIONS': 'D:\\Development\\JetBrains\\DataGrip jetbra\\jetbra\\vmoptions\\rider.vmoptions','RUBYMINE_VM_OPTIONS': 'D:\\Development\\JetBrains\\DataGrip jetbra\\jetbra\\vmoptions\\rubymine.vmoptions','SCOOP': 'D:\\Development\\Scoop','SESSIONNAME': 'Console','STUDIO_VM_OPTIONS': 'D:\\Development\\JetBrains\\DataGrip jetbra\\jetbra\\vmoptions\\studio.vmoptions','SYSTEMDRIVE': 'C:','SYSTEMROOT': 'C:\\WINDOWS','TEMP': 'C:\\Users\\<USER>\\AppData\\Local\\Temp','TMP': 'C:\\Users\\<USER>\\AppData\\Local\\Temp','TOOLBOX_VERSION': '2.7.0.48109','USERDOMAIN': 'DESKTOP-MRIK9I3','USERDOMAIN_ROAMINGPROFILE': 'DESKTOP-MRIK9I3','USERNAME': 'JieMoc','USERPROFILE': 'C:\\Users\\<USER>\\Development\\JetBrains\\DataGrip jetbra\\jetbra\\vmoptions\\webide.vmoptions','WEBSTORM_VM_OPTIONS': 'D:\\Development\\JetBrains\\DataGrip jetbra\\jetbra\\vmoptions\\webstorm.vmoptions','WINDIR': 'C:\\WINDOWS','NUITKA_PYTHON_EXE_PATH': 'D:\\Development\\Python\\anaconda3\\envs\\python_312\\python.exe','NUITKA_PACKAGE_DIR': 'D:\\Development\\Python\\anaconda3\\envs\\python_312\\Lib\\site-packages\\nuitka','NUITKA_CACHE_DIR_DOWNLOADS': 'C:\\Users\\<USER>\\AppData\\Local\\Nuitka\\Nuitka\\Cache\\DOWNLO~1','_NUITKA_ONEFILE_CHILD_GRACE_TIME_INT': '5000','_NUITKA_BUILD_DEFINITIONS_CATALOG': '_NUITKA_ONEFILE_CHILD_GRACE_TIME_INT,_NUITKA_BUILD_DEFINITIONS_CATALOG','NUITKA_QUIET': '0'},
    shell=False
)