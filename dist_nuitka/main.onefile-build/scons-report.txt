CC=<clcache>
CCCOM=${TEMPFILE("$CC $_MSVC_OUTPUT_FLAG /c $CHANGED_SOURCES $CFLAGS $CCFLAGS $_CCCOMCOM","$CCCOMSTR")}
CFILESUFFIX=.c
CLCACHE_DIR=C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\clcache
CLCACHE_STATS=D:\Project\PycharmProjects\lawyee\Wechat-Auto\dist_nuitka\main.onefile-build\clcache-stats.11556.txt
CPPDEFINES=['_NUITKA_STANDALONE_MODE', '_NUITKA_ONEFILE_MODE', '_NUITKA_ONEFILE_TEMP_BOOL', '_NUITKA_DLL_MODE', '_NUITKA_ONEFILE_DLL_MODE', '_WIN32_WINNT=0x0601', '__NUITKA_NO_ASSERT__', '_NUITKA_WINMAIN_ENTRY_POINT', '_NUITKA_EXE_MODE', '_NUITKA_PLUGIN_MULTIPROCESSING_ENABLED=1']
CPPDEFPREFIX=/D
CPPDEFSUFFIX=
CPPPATH=['D:\\Development\\Python\\anaconda3\\envs\\python_312\\Lib\\site-packages\\nuitka\\build\\inline_copy\\zlib', '.', 'D:\\Development\\Python\\anaconda3\\envs\\python_312\\Lib\\site-packages\\nuitka\\build\\include', 'D:\\Development\\Python\\anaconda3\\envs\\python_312\\Lib\\site-packages\\nuitka\\build\\static_src', 'D:\\Development\\Python\\anaconda3\\envs\\python_312\\Lib\\site-packages\\nuitka\\build\\inline_copy\\zstd']
CPPSUFFIXES=['.c', '.C', '.cxx', '.cpp', '.c++', '.cc', '.h', '.H', '.hxx', '.hpp', '.hh', '.F', '.fpp', '.FPP', '.m', '.mm', '.S', '.spp', '.SPP', '.sx']
CXX=<clcache>
CXXCOM=${TEMPFILE("$CXX $_MSVC_OUTPUT_FLAG /c $CHANGED_SOURCES $CXXFLAGS $CCFLAGS $_CCCOMCOM","$CXXCOMSTR")}
CXXFILESUFFIX=.cc
HOST_ARCH=x86_64
HOST_OS=win32
INCPREFIX=/I
INCSUFFIX=
LDMODULE=$SHLINK
LDMODULEFLAGS=$SHLINKFLAGS
LDMODULEPREFIX=$SHLIBPREFIX
LDMODULESUFFIX=$SHLIBSUFFIX
LIBDIRPREFIX=/LIBPATH:
LIBDIRSUFFIX=
LIBLINKPREFIX=
LIBLINKSUFFIX=$LIBSUFFIX
LIBPATH=[]
LIBPREFIX=
LIBPREFIXES=['$LIBPREFIX']
LIBS=['Shell32', 'PsApi']
LIBSUFFIX=.lib
LIBSUFFIXES=['$LIBSUFFIX']
LINK=link
MSVSBUILDCOM=$MSVSSCONSCOM "$MSVSBUILDTARGET"
MSVSCLEANCOM=$MSVSSCONSCOM -c "$MSVSBUILDTARGET"
MSVSENCODING=utf-8
MSVSPROJECTSUFFIX=${GET_MSVSPROJECTSUFFIX}
MSVSREBUILDCOM=$MSVSSCONSCOM "$MSVSBUILDTARGET"
MSVSSCONSCOM=$MSVSSCONS $MSVSSCONSFLAGS
MSVSSCONSFLAGS=-C "${MSVSSCONSCRIPT.dir.get_abspath()}" -f ${MSVSSCONSCRIPT.name}
MSVSSOLUTIONSUFFIX=${GET_MSVSSOLUTIONSUFFIX}
MSVS_VERSION=14.3
MT=mt
MTEXECOM=-$MT $MTFLAGS -manifest ${TARGET}.manifest $_MANIFEST_SOURCES -outputresource:$TARGET;1
MTSHLIBCOM=-$MT $MTFLAGS -manifest ${TARGET}.manifest $_MANIFEST_SOURCES -outputresource:$TARGET;2
OBJPREFIX=
OBJSUFFIX=.obj
PCHCOM=$CXX /Fo${TARGETS[1]} $CXXFLAGS $CCFLAGS $CPPFLAGS $_CPPDEFFLAGS $_CPPINCFLAGS /c $SOURCES /Yc$PCHSTOP /Fp${TARGETS[0]} $CCPDBFLAGS $PCHPDBFLAGS
PCHPDBFLAGS=
PLATFORM=win32
PROGPREFIX=
PROGSUFFIX=.exe
RC=rc
RCCOM=$RC $_CPPDEFFLAGS $_CPPINCFLAGS $RCFLAGS /fo$TARGET $SOURCES
RCSUFFIXES=['.rc', '.rc2']
REGSVR=C:\WINDOWS\System32\regsvr32
REGSVRCOM=$REGSVR $REGSVRFLAGS ${TARGET.windows}
REGSVRFLAGS=/s 
SHCC=$CC
SHCCCOM=${TEMPFILE("$SHCC $_MSVC_OUTPUT_FLAG /c $CHANGED_SOURCES $SHCFLAGS $SHCCFLAGS $_CCCOMCOM","$SHCCCOMSTR")}
SHCXX=$CXX
SHCXXCOM=${TEMPFILE("$SHCXX $_MSVC_OUTPUT_FLAG /c $CHANGED_SOURCES $SHCXXFLAGS $SHCCFLAGS $_CCCOMCOM","$SHCXXCOMSTR")}
SHELL=C:\WINDOWS\System32\cmd.exe
SHLIBPREFIX=
SHLIBSUFFIX=.dll
SHLINK=$LINK
SHOBJPREFIX=$OBJPREFIX
SHOBJSUFFIX=$OBJSUFFIX
TARGET_ARCH=x86_64
TEMPFILEARGJOIN=

TEMPFILEPREFIX=@
TOOLS=['default', 'mslink', 'msvc', 'msvs']
VSWHERE=C:\Program Files (x86)\Microsoft Visual Studio\Installer\vswhere.exe
WINDOWSDEFPREFIX=
WINDOWSDEFSUFFIX=.def
WINDOWSEXPPREFIX=
WINDOWSEXPSUFFIX=.exp
WINDOWSPROGMANIFESTPREFIX=
WINDOWSPROGMANIFESTSUFFIX=${PROGSUFFIX}.manifest
WINDOWSSHLIBMANIFESTPREFIX=
WINDOWSSHLIBMANIFESTSUFFIX=${SHLIBSUFFIX}.manifest
WindowsSDKVersion=10.0.22621.0
gcc_mode=False
clang_mode=False
msvc_mode=True
mingw_mode=False
clangcl_mode=False
PATH=C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\VC\Tools\MSVC\14.37.32822\bin\HostX64\x64;C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\VC\Tools\MSVC\14.37.32822\bin\HostX64\x64;C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\Common7\IDE\VC\VCPackages;C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\Common7\IDE\CommonExtensions\Microsoft\TestWindow;C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\MSBuild\Current\bin\Roslyn;C:\Program Files (x86)\Windows Kits\10\bin\10.0.22621.0\\x64;C:\Program Files (x86)\Windows Kits\10\bin\\x64;C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\\MSBuild\Current\Bin\amd64;C:\Windows\Microsoft.NET\Framework64\v4.0.30319;C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\Common7\IDE\;C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\Common7\Tools\;D:\Development\Python\anaconda3\condabin;C:\WINDOWS\System32;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0;C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin;C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\Ninja;C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\Common7\IDE\VC\Linux\bin\ConnectionManagerExe;C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\cv2\../../x64/vc14/bin;D:\Development\Python\anaconda3\envs\python_312\Lib\site-packages\PySide6;D:\Development\Python\anaconda3\envs\python_312;D:\Development\Python\anaconda3\envs\python_312\Library\mingw-w64\bin;D:\Development\Python\anaconda3\envs\python_312\Library\usr\bin;D:\Development\Python\anaconda3\envs\python_312\Library\bin;D:\Development\Python\anaconda3\envs\python_312\Scripts;D:\Development\Python\anaconda3\envs\python_312\bin;C:\Program Files\Common Files\Oracle\Java\javapath;C:\Users\<USER>\.nvmd\bin;D:\Development\Environment\apache-maven-3.8.3\bin;D:\Development\Tools\VMware\VMware Workstation\bin;C:\Program Files (x86)\Common Files\Oracle\Java\javapath;C:\WINDOWS;C:\WINDOWS\System32\OpenSSH;C:\Recovery\OEM\Backup;C:\Program Files\dotnet;D:\Development\Python\anaconda3;D:\Development\Python\anaconda3\Scripts;D:\Development\Python\anaconda3\Library\lib;D:\Development\Tools\Git\cmd;D:\Development\Tools\TortoiseSVN\bin;D:\Development\Web\nvm;D:\Development\Web\nodejs;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Local\JetBrains\Toolbox\scripts;D:\Development\JetBrains\Toolbox\scripts;.;C:\Users\<USER>\AppData\Local\UniGetUI\Chocolatey\bin;D:\Development\Scoop\shims;D:\Development\Java\Java-8u181\jdk1.8.0_181\BIN;C:\Users\<USER>\AppData\Local\Programs\Ollama;C:\Users\<USER>\.cache\lm-studio\bin;D:\Development\Tools\Microsoft VS Code\bin;D:\Development\Python\anaconda3\Library\Bin
TARGET=D:\Project\PycharmProjects\lawyee\Wechat-Auto\dist_nuitka\微信公众号自动化管理工具.exe
